import React from 'react'
import { Text, TextEnum } from '@/components/business/text'
import { OutlineItem } from '@/store/deep-research'
import { DraggableOutlineItem } from './DraggableOutlineItem'
import { DragState } from '../hooks/useDragDrop'

interface OutlinePreviewProps {
  items: OutlineItem[]
  dragState: DragState
  expandedItems: Set<string>
  onToggleExpand: (sequence: string) => void
  onDragStart: (e: React.DragEvent, item: OutlineItem, path: number[]) => void
  onDragOver: (e: React.DragEvent, targetPath: number[], position: 'before' | 'after' | 'inside') => void
  onDragLeave: (e: React.DragEvent) => void
  onDrop: (e: React.DragEvent, targetPath: number[], position: 'before' | 'after' | 'inside') => void
  onDragEnd: (e: React.DragEvent) => void
}

export const OutlinePreview: React.FC<OutlinePreviewProps> = ({
  items,
  dragState,
  expandedItems,
  onToggleExpand,
  onDragStart,
  onDragOver,
  onDragLeave,
  onDrop,
  onDragEnd,
}) => {
  return (
    <div
      className="h-full w-[290px] overflow-auto border-l border-border p-4"
      style={{ scrollbarGutter: 'stable' }}
    >
      <Text type={TextEnum.H5} className="text-secondary-black-1">
        Table of Contents Preview
      </Text>
      <div className="draggable-outline-container">
        {items?.map((item, index) => (
          <DraggableOutlineItem
            key={item.id}
            item={item}
            path={[index]}
            level={0}
            dragState={dragState}
            expandedItems={expandedItems}
            onToggleExpand={onToggleExpand}
            onDragStart={onDragStart}
            onDragOver={onDragOver}
            onDragLeave={onDragLeave}
            onDrop={onDrop}
            onDragEnd={onDragEnd}
          />
        ))}
      </div>
    </div>
  )
}
