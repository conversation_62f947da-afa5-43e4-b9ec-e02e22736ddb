import React from 'react'
import clsx from 'clsx'
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline'
import { Bars3Icon } from '@heroicons/react/24/solid'
import { OutlineItem } from '@/store/deep-research'
import { DragState } from '../hooks/useDragDrop'

interface DraggableOutlineItemProps {
  item: OutlineItem
  path: number[]
  level: number
  dragState: DragState
  expandedItems: Set<string>
  onToggleExpand: (sequence: string) => void
  onDragStart: (e: React.DragEvent, item: OutlineItem, path: number[]) => void
  onDragOver: (e: React.DragEvent, targetPath: number[], position: 'before' | 'after' | 'inside') => void
  onDragLeave: (e: React.DragEvent) => void
  onDrop: (e: React.DragEvent, targetPath: number[], position: 'before' | 'after' | 'inside') => void
  onDragEnd: (e: React.DragEvent) => void
}

export const DraggableOutlineItem: React.FC<DraggableOutlineItemProps> = ({
  item,
  path,
  level,
  dragState,
  expandedItems,
  onToggleExpand,
  onDragStart,
  onDragOver,
  onDragLeave,
  onDrop,
  onDragEnd,
}) => {
  const isBeingDragged =
    dragState.draggedPath && JSON.stringify(dragState.draggedPath) === JSON.stringify(path)
  const isDropTarget =
    dragState.dropTargetPath && JSON.stringify(dragState.dropTargetPath) === JSON.stringify(path)
  const isExpanded = expandedItems.has(item.sequence as string)
  const hasChildren = item.children && item.children.length > 0

  return (
    <div className={`draggable-item-container level-${level}`}>
      {/* 拖拽区域 - 上方 */}
      <div
        className={clsx(
          'drop-zone drop-zone-before h-2 transition-all duration-200',
          isDropTarget &&
            dragState.dropPosition === 'before' &&
            'border-t-2 border-blue-500 bg-blue-200'
        )}
        onDragOver={(e) => onDragOver(e, path, 'before')}
        onDragLeave={onDragLeave}
        onDrop={(e) => onDrop(e, path, 'before')}
      />

      {/* 主要内容区域 */}
      <div
        draggable={true}
        className={clsx(
          'outline-item group relative cursor-move border-l-2 border-transparent transition-all duration-200 hover:border-blue-300',
          isBeingDragged && 'z-50 rotate-1 opacity-50',
          isDropTarget && dragState.dropPosition === 'inside' && 'border-blue-500 bg-blue-50'
        )}
        style={{ marginLeft: `${level * 16}px` }}
        onDragStart={(e) => onDragStart(e, item, path)}
        onDragOver={(e) => onDragOver(e, path, 'inside')}
        onDragLeave={onDragLeave}
        onDrop={(e) => onDrop(e, path, 'inside')}
        onDragEnd={onDragEnd}
      >
        {/* 拖拽手柄 */}
        <div className="drag-handle absolute left-1 top-1/2 -translate-y-1/2 transform opacity-0 transition-opacity duration-200 group-hover:opacity-100">
          <Bars3Icon className="h-3 w-3 text-gray-400 hover:text-blue-500" />
        </div>

        {/* 内容 */}
        <div className="ml-6 flex items-center py-1">
          {/* 展开/收起按钮 */}
          {hasChildren && (
            <button
              onClick={(e) => {
                e.stopPropagation()
                e.preventDefault()
                onToggleExpand(item.sequence as string)
              }}
              className="expand-button mr-2 flex h-4 w-4 items-center justify-center rounded hover:bg-gray-100"
              draggable={false}
            >
              {isExpanded ? (
                <ChevronDownIcon className="h-3 w-3 text-gray-500" />
              ) : (
                <ChevronRightIcon className="h-3 w-3 text-gray-500" />
              )}
            </button>
          )}

          {/* 标题 */}
          <div className="flex-1 text-sm font-medium text-secondary-black-1">
            {item.sequence}. {item.title}
          </div>
        </div>
      </div>

      {/* 子项目 - 带展开/收起动画 */}
      {hasChildren && (
        <div
          className={clsx(
            'children-container overflow-hidden transition-all duration-300 ease-in-out',
            isExpanded ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'
          )}
          style={{ perspective: '1000px' }}
        >
          <div
            className="transition-all duration-300 ease-in-out"
            style={{
              transform: isExpanded ? 'rotateX(0deg) scaleY(1)' : 'rotateX(-90deg) scaleY(0.8)',
              transformOrigin: 'center top',
              opacity: isExpanded ? 1 : 0,
            }}
          >
            {item.children!.map((child, childIndex) => (
              <DraggableOutlineItem
                key={child.id}
                item={child}
                path={[...path, childIndex]}
                level={level + 1}
                dragState={dragState}
                expandedItems={expandedItems}
                onToggleExpand={onToggleExpand}
                onDragStart={onDragStart}
                onDragOver={onDragOver}
                onDragLeave={onDragLeave}
                onDrop={onDrop}
                onDragEnd={onDragEnd}
              />
            ))}
          </div>
        </div>
      )}

      {/* 拖拽区域 - 下方 */}
      <div
        className={clsx(
          'drop-zone drop-zone-after h-2 transition-all duration-200',
          isDropTarget &&
            dragState.dropPosition === 'after' &&
            'border-b-2 border-blue-500 bg-blue-200'
        )}
        onDragOver={(e) => onDragOver(e, path, 'after')}
        onDragLeave={onDragLeave}
        onDrop={(e) => onDrop(e, path, 'after')}
      />
    </div>
  )
}
