import React from 'react'
import { OutlineItem } from '@/store/deep-research'
import { EditableOutlineItem } from './EditableOutlineItem'

interface OutlineEditorProps {
  items: OutlineItem[]
  showDescriptions: boolean
  expandedItems: Set<string>
  onToggleExpand: (sequence: string) => void
  onUpdateItem: (itemId: string, field: 'title' | 'description', value: string) => void
}

export const OutlineEditor: React.FC<OutlineEditorProps> = ({
  items,
  showDescriptions,
  expandedItems,
  onToggleExpand,
  onUpdateItem,
}) => {
  return (
    <div
      className="h-full w-[calc(100%-290px)] overflow-auto border-r border-border p-4"
      style={{ scrollbarGutter: 'stable' }}
    >
      {items?.map((item, index) => (
        <EditableOutlineItem
          key={item.id}
          item={item}
          isShowDescription={showDescriptions}
          isLast={index === items.length - 1}
          level={0}
          expandedItems={expandedItems}
          onToggleExpand={onToggleExpand}
          onUpdateItem={onUpdateItem}
        />
      ))}
    </div>
  )
}
