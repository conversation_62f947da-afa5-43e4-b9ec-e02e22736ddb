# ModalOutline 组件重构说明

## 📁 文件结构

```
src/pages/basic-search/management/modal/
├── ModalOutline.tsx                    # 原始组件（保留）
├── ModalOutlineRefactored.tsx          # 重构后的主组件
├── README.md                           # 说明文档
├── components/                         # 子组件
│   ├── index.ts                       # 组件导出
│   ├── DragDropStyles.ts              # 拖拽样式定义
│   ├── DraggableOutlineItem.tsx       # 可拖拽大纲项目
│   ├── EditableOutlineItem.tsx        # 可编辑大纲项目
│   ├── OutlineEditor.tsx              # 左侧编辑器
│   └── OutlinePreview.tsx             # 右侧预览
├── hooks/                             # 自定义 Hooks
│   ├── index.ts                       # Hook 导出
│   └── useDragDrop.ts                 # 拖拽逻辑 Hook
└── utils/                             # 工具函数
    ├── index.ts                       # 工具导出
    └── outlineUtils.ts                # 大纲操作工具
```

## 🎯 重构目标

1. **单一职责原则**：每个组件只负责一个特定功能
2. **可复用性**：组件可以在其他地方复用
3. **可维护性**：代码结构清晰，易于维护和扩展
4. **性能优化**：减少不必要的重渲染
5. **类型安全**：完整的 TypeScript 类型定义

## 📦 组件说明

### 主组件
- **ModalOutlineRefactored.tsx**: 重构后的主组件，负责整体布局和状态管理

### 子组件
- **DraggableOutlineItem.tsx**: 可拖拽的大纲项目，支持嵌套拖拽
- **EditableOutlineItem.tsx**: 可编辑的大纲项目，支持内容修改
- **OutlineEditor.tsx**: 左侧编辑区域容器
- **OutlinePreview.tsx**: 右侧预览区域容器

### Hooks
- **useDragDrop.ts**: 拖拽逻辑封装，包含所有拖拽相关的状态和方法

### 工具函数
- **outlineUtils.ts**: 大纲操作相关的纯函数，如数据更新、路径操作等
- **DragDropStyles.ts**: 拖拽样式定义和注入逻辑

## 🔄 使用方式

### 替换原组件
```tsx
// 原来的导入
import { ModalOutline } from './ModalOutline'

// 替换为重构版本
import { ModalOutline } from './ModalOutlineRefactored'
```

### 单独使用子组件
```tsx
import { DraggableOutlineItem, OutlineEditor } from './components'
import { useDragDrop } from './hooks'
import { collectAllSequences } from './utils'
```

## ✨ 重构优势

### 1. 代码分离
- **样式分离**: 拖拽样式独立到 `DragDropStyles.ts`
- **逻辑分离**: 拖拽逻辑封装到 `useDragDrop` Hook
- **工具分离**: 纯函数提取到 `outlineUtils.ts`

### 2. 组件职责明确
- **ModalOutlineRefactored**: 整体布局和状态协调
- **OutlineEditor**: 左侧编辑功能
- **OutlinePreview**: 右侧预览和拖拽功能
- **DraggableOutlineItem**: 单个可拖拽项目
- **EditableOutlineItem**: 单个可编辑项目

### 3. 可测试性提升
- 每个组件和函数都可以独立测试
- 纯函数易于单元测试
- Hook 可以使用 `@testing-library/react-hooks` 测试

### 4. 性能优化
- 组件粒度更细，减少不必要的重渲染
- 使用 React.memo 可以进一步优化性能
- 状态管理更精确

## 🚀 扩展建议

### 1. 添加 React.memo 优化
```tsx
export const DraggableOutlineItem = React.memo<DraggableOutlineItemProps>(({ ... }) => {
  // 组件实现
})
```

### 2. 添加错误边界
```tsx
export const OutlineErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // 错误边界实现
}
```

### 3. 添加加载状态
```tsx
export const OutlineLoader: React.FC = () => {
  // 加载状态组件
}
```

### 4. 添加单元测试
```tsx
// __tests__/outlineUtils.test.ts
describe('outlineUtils', () => {
  test('collectAllSequences should return all sequences', () => {
    // 测试实现
  })
})
```

## 📝 迁移指南

1. **保留原组件**: 原始 `ModalOutline.tsx` 保持不变，确保向后兼容
2. **逐步迁移**: 可以逐步将使用原组件的地方替换为新组件
3. **测试验证**: 确保新组件功能与原组件一致
4. **性能监控**: 监控重构后的性能表现

## 🔧 开发建议

1. **组件开发**: 优先开发和测试单个子组件
2. **Hook 开发**: 确保 Hook 的纯净性和可复用性
3. **工具函数**: 保持函数的纯净性，避免副作用
4. **类型定义**: 完善 TypeScript 类型定义
5. **文档维护**: 及时更新组件文档和使用示例
