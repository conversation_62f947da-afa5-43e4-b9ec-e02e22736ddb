import { Modal } from '@/components/business/modal'
import { Button } from '@/components/ui/button'
import { Text, TextEnum } from '@/components/business/text'
import { EyeSlashIcon, EyeIcon } from '@heroicons/react/24/outline'
import { XMarkIcon, CheckIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { useEffect, useState } from 'react'
import { useDeepResearchStore } from '@/store/deep-research'
import { Loader } from 'lucide-react'
// import { confirmRequirementApi } from '@/api/deep-research'

// 导入拆分的组件和工具
import { injectDragStyles } from './components/DragDropStyles'
import { collectAllSequences, updateOutlineItemContent } from './utils/outlineUtils'
import { useDragDrop } from './hooks/useDragDrop'
import { OutlineEditor } from './components/OutlineEditor'
import { OutlinePreview } from './components/OutlinePreview'

// 注入拖拽样式
injectDragStyles()

export const ModalOutline = ({
  open,
  handleCancel,
}: {
  open: boolean
  handleCancel: () => void
}) => {
  const { t } = useTranslation(Namespace.DEEPRESEARCH)
  const { requirement, outline, setOutline } = useDeepResearchStore()
  const [isFetched, setIsFetched] = useState(false)
  const [inputValue, setInputValue] = useState(requirement.问题确认)
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const [showDescriptions, setShowDescriptions] = useState(true)

  // 使用拖拽 hook
  const { dragState, handleDragStart, handleDragOver, handleDragLeave, handleDrop, handleDragEnd } =
    useDragDrop()

  // 更新大纲项目内容
  const updateOutlineItem = (itemId: string, field: 'title' | 'description', value: string) => {
    if (!outline.报告大纲) return

    const updatedItems = updateOutlineItemContent(outline.报告大纲, itemId, field, value)
    const newOutline = {
      ...outline,
      报告大纲: updatedItems,
    }
    setOutline(newOutline)
  }

  // 展开/收起控制
  const toggleExpand = (sequence: string) => {
    setExpandedItems((prev) => {
      const newSet = new Set(prev)
      if (newSet.has(sequence)) {
        newSet.delete(sequence)
      } else {
        newSet.add(sequence)
      }
      return newSet
    })
  }

  // 全部展开/收起
  const toggleExpandAll = () => {
    if (!outline.报告大纲) return

    const allSequences = collectAllSequences(outline.报告大纲)
    const isAllExpanded = allSequences.every((seq) => expandedItems.has(seq))

    if (isAllExpanded) {
      setExpandedItems(new Set())
    } else {
      setExpandedItems(new Set(allSequences))
    }
  }

  // 初始化效果
  useEffect(() => {
    if (open) {
      setInputValue(requirement.问题确认)
      // 默认展开所有项目
      if (outline.报告大纲) {
        const allSequences = collectAllSequences(outline.报告大纲)
        setExpandedItems(new Set(allSequences))
      }
    } else {
      setIsFetched(false)
    }
  }, [open, outline.报告大纲, requirement.问题确认])

  // 确认处理
  const onConfirm = () => {
    // setIsFetched(true)
    // 这里可以添加确认逻辑
    // confirmRequirementApi({ confirmation: inputValue, taskId })
    handleCancel()
  }

  // 检查是否全部展开
  const isAllExpanded = outline.报告大纲
    ? collectAllSequences(outline.报告大纲).every((seq) => expandedItems.has(seq))
    : false

  return (
    <Modal open={open} onOpenChange={handleCancel}>
      <div className='flex h-[calc(100vh-100px)] w-[calc(100vw-100px)] flex-col'>
        {/* 头部 */}
        <Text
          type={TextEnum.H4}
          className='flex items-center justify-between border-b border-border p-6'>
          <div className='flex items-center space-x-4'>
            <span>Outline Confirmation</span>

            {/* 控制按钮 */}
            <a
              onClick={toggleExpandAll}
              className='flex cursor-pointer select-none items-center text-sm font-normal'>
              {isAllExpanded ? '收起全部' : '展开全部'}
            </a>

            <a
              onClick={() => setShowDescriptions(!showDescriptions)}
              className='flex cursor-pointer select-none items-center text-sm font-normal'>
              {showDescriptions ? (
                <>
                  <EyeSlashIcon className='mr-1 size-4' />
                  Hide All Descriptions
                </>
              ) : (
                <>
                  <EyeIcon className='mr-1 size-4' />
                  Show All Descriptions
                </>
              )}
            </a>
          </div>

          <XMarkIcon onClick={handleCancel} className='h-5 w-5 cursor-pointer' />
        </Text>

        {/* 主体内容 */}
        <div className='pb-4'>
          <div className='flex h-[calc(100vh-200px)] px-2'>
            {/* 左侧编辑区域 */}
            <OutlineEditor
              items={outline.报告大纲 || []}
              showDescriptions={showDescriptions}
              expandedItems={expandedItems}
              onToggleExpand={toggleExpand}
              onUpdateItem={updateOutlineItem}
            />

            {/* 右侧预览区域 */}
            <OutlinePreview
              items={outline.报告大纲 || []}
              dragState={dragState}
              expandedItems={expandedItems}
              onToggleExpand={toggleExpand}
              onDragStart={handleDragStart}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onDragEnd={handleDragEnd}
            />
          </div>

          {/* 底部按钮 */}
          <div className='border-t border-border px-6 pt-4 text-right'>
            <Button variant='secondary' size='sm' className='px-4' onClick={handleCancel}>
              {t('modal.cancel')}
            </Button>
            <Button
              size='sm'
              className='ml-2 px-4'
              disabled={!inputValue || !requirement.合规判定 || isFetched}
              onClick={onConfirm}>
              {isFetched ? (
                <Loader className='mr-1 size-4 animate-spin' />
              ) : (
                <CheckIcon className='mr-1 size-4' />
              )}
              {t('modal.confirm')}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  )
}
