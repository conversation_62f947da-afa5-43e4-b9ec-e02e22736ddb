import { useState } from 'react'
import { OutlineItem, useDeepResearchStore } from '@/store/deep-research'
import {
  isValidDropTarget,
  removeItemByPath,
  insertItemAtPath,
  processOutlineDataRecursive,
} from '../utils/outlineUtils'

export interface DragState {
  draggedItem: OutlineItem | null
  draggedPath: number[] | null
  dropTargetPath: number[] | null
  dropPosition: 'before' | 'after' | 'inside' | null
  isDragging: boolean
}

export const useDragDrop = () => {
  const { outline, setOutline } = useDeepResearchStore()
  const [dragState, setDragState] = useState<DragState>({
    draggedItem: null,
    draggedPath: null,
    dropTargetPath: null,
    dropPosition: null,
    isDragging: false,
  })

  // 拖拽开始
  const handleDragStart = (e: React.DragEvent, item: OutlineItem, path: number[]) => {
    e.dataTransfer.setData(
      'text/plain',
      JSON.stringify({
        itemId: item.id,
        sourcePath: path,
      })
    )
    e.dataTransfer.effectAllowed = 'move'
    setDragState({
      draggedItem: item,
      draggedPath: path,
      dropTargetPath: null,
      dropPosition: null,
      isDragging: true,
    })
    ;(e.currentTarget as HTMLElement).classList.add('dragging')
  }

  // 拖拽悬停
  const handleDragOver = (
    e: React.DragEvent,
    targetPath: number[],
    position: 'before' | 'after' | 'inside'
  ) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
    if (!isValidDropTarget(dragState.draggedPath, targetPath, position)) {
      return
    }
    setDragState((prev) => ({
      ...prev,
      dropTargetPath: targetPath,
      dropPosition: position,
    }))
    const target = e.currentTarget as HTMLElement
    target.classList.add('drag-over')
  }

  // 拖拽离开
  const handleDragLeave = (e: React.DragEvent) => {
    const target = e.currentTarget as HTMLElement
    target.classList.remove('drag-over')
  }

  // 拖拽放置
  const handleDrop = (e: React.DragEvent, targetPath: number[], position: 'before' | 'after' | 'inside') => {
    e.preventDefault()
    const target = e.currentTarget as HTMLElement
    target.classList.remove('drag-over')

    if (!dragState.draggedPath || !dragState.draggedItem) return

    if (!isValidDropTarget(dragState.draggedPath, targetPath, position)) {
      return
    }

    reorderNestedOutlineItems(dragState.draggedPath, targetPath, position)
    cleanupDragState()
  }

  // 拖拽结束
  const handleDragEnd = (e: React.DragEvent) => {
    cleanupDragState()
  }

  // 清理拖拽状态
  const cleanupDragState = () => {
    setDragState({
      draggedItem: null,
      draggedPath: null,
      dropTargetPath: null,
      dropPosition: null,
      isDragging: false,
    })

    // 清除所有拖拽相关样式
    document.querySelectorAll('.dragging, .drag-over').forEach((el) => {
      el.classList.remove('dragging', 'drag-over')
    })
  }

  // 嵌套重排序逻辑
  const reorderNestedOutlineItems = (
    sourcePath: number[],
    targetPath: number[],
    position: 'before' | 'after' | 'inside'
  ) => {
    if (!outline.报告大纲) return

    const items = JSON.parse(JSON.stringify(outline.报告大纲)) // 深拷贝

    // 移除源项目
    const draggedItem = removeItemByPath(items, sourcePath)
    if (!draggedItem) return

    // 调整目标路径（如果源项目在目标项目之前，需要调整索引）
    const adjustedTargetPath = [...targetPath]
    if (sourcePath.length === targetPath.length) {
      // 同级移动
      const lastSourceIndex = sourcePath[sourcePath.length - 1]
      const lastTargetIndex = targetPath[targetPath.length - 1]

      if (lastSourceIndex < lastTargetIndex) {
        adjustedTargetPath[adjustedTargetPath.length - 1] = lastTargetIndex - 1
      }
    }

    // 插入到目标位置
    insertItemAtPath(items, draggedItem, adjustedTargetPath, position)

    // 重新处理数据（重新计算序号等）
    const processedItems = processOutlineDataRecursive(items)

    // 更新 store
    const newOutline = {
      ...outline,
      报告大纲: processedItems,
    }
    setOutline(newOutline)
    console.log('嵌套重排序后的数据:', processedItems)
  }

  return {
    dragState,
    handleDragStart,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    handleDragEnd,
    cleanupDragState,
  }
}
