import React from 'react'
import clsx from 'clsx'
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline'
import { OutlineItem } from '@/store/deep-research'
import { Textinput } from '@/components/business/text-input'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'

interface EditableOutlineItemProps {
  item: OutlineItem
  isShowDescription: boolean
  isLast?: boolean
  level?: number
  expandedItems: Set<string>
  onToggleExpand: (sequence: string) => void
  onUpdateItem: (itemId: string, field: 'title' | 'description', value: string) => void
}

export const EditableOutlineItem: React.FC<EditableOutlineItemProps> = ({
  item,
  isShowDescription,
  isLast = false,
  level = 0,
  expandedItems,
  onToggleExpand,
  onUpdateItem,
}) => {
  const { t } = useTranslation(Namespace.DEEPRESEARCH)
  const isExpanded = expandedItems.has(item.sequence as string)

  return (
    <div className={clsx('relative', level === 0 && !isLast && 'pb-2')}>
      {/* 时间线样式 - 仅对子级项目显示 */}
      {level > 0 && (
        <>
          {/* 连接线 */}
          <div
            className="absolute left-0 top-0 h-full w-px bg-gray-200"
            style={{ left: `${(level - 1) * 24 + 12}px` }}
          />
          {/* 圆点 */}
          <div
            className="absolute top-2 h-2 w-2 rounded-full bg-blue-500"
            style={{ left: `${(level - 1) * 24 + 8}px` }}
          />
          {/* 水平连接线 */}
          <div
            className="absolute top-3 h-px w-4 bg-gray-200"
            style={{ left: `${(level - 1) * 24 + 16}px` }}
          />
        </>
      )}

      <div
        className="flex cursor-pointer items-start"
        style={{ paddingLeft: `${level * 24}px` }}
        onClick={() => {
          if (item.children && item.children.length > 0) {
            onToggleExpand(item.sequence as string)
          }
        }}
      >
        {/* 展开/收起图标 */}
        <div className="mr-2 mt-1 flex h-4 w-4 items-center justify-center">
          {item.children && item.children.length > 0 && (
            <div className="flex h-4 w-4 items-center justify-center rounded hover:bg-gray-100">
              {isExpanded ? (
                <ChevronDownIcon className="h-3 w-3 text-gray-500" />
              ) : (
                <ChevronRightIcon className="h-3 w-3 text-gray-500" />
              )}
            </div>
          )}
        </div>

        {/* 内容区域 */}
        <div className="flex-1">
          {/* 标题 */}
          <div className="mb-1 text-sm font-medium text-secondary-black-1">
            {item.sequence}. {item.title}
          </div>

          {/* 描述 */}
          {item.description && (
            <div className="mb-2 text-sm text-secondary-black-2">{item.description}</div>
          )}

          {/* 编辑区域 */}
          {isShowDescription && (
            <div className="mb-2 mt-1 rounded-md border border-border pb-2 text-secondary-black-2">
              <div className="font-bold text-secondary-black-1">
                <Textinput
                  placeholder={t('modal.titlePlaceholder') || '请输入标题'}
                  isStopEnterPrevent={false}
                  className="border-none px-2 text-[16px] leading-6 text-secondary-black-2 ring-offset-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
                  value={item.title}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                    onUpdateItem(item.id!, 'title', e.target.value)
                  }
                />
              </div>
              <Textinput
                placeholder={t('modal.descriptionPlaceholder')}
                isStopEnterPrevent={false}
                className="border-none px-2 py-0 text-sm leading-6 text-secondary-black-2 ring-offset-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
                value={item.description}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                  onUpdateItem(item.id!, 'description', e.target.value)
                }
              />
            </div>
          )}

          {/* 子项目 */}
          {item.children && (
            <div
              className={clsx(
                'overflow-hidden transition-all duration-300 ease-in-out',
                isShowDescription ? 'ml-0' : 'ml-9',
                isExpanded ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'
              )}
              style={{ perspective: '1000px' }}
            >
              <div
                className="transition-all duration-300 ease-in-out"
                style={{
                  transform: isExpanded ? 'rotateX(0deg) scaleY(1)' : 'rotateX(-90deg) scaleY(0.8)',
                  transformOrigin: 'center top',
                  opacity: isExpanded ? 1 : 0,
                }}
              >
                {item.children.map((child, index) => (
                  <EditableOutlineItem
                    key={child.id}
                    item={child}
                    isShowDescription={isShowDescription}
                    isLast={index === item.children!.length - 1}
                    level={level + 1}
                    expandedItems={expandedItems}
                    onToggleExpand={onToggleExpand}
                    onUpdateItem={onUpdateItem}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
