// 拖拽样式定义
export const dragStyles = `
  .dragging {
    opacity: 0.5 !important;
    transform: rotate(2deg) !important;
    z-index: 1000 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }
  
  .drag-over {
    border-left-color: #3b82f6 !important;
    background-color: rgba(59, 130, 246, 0.1) !important;
  }
  
  .drag-handle {
    cursor: grab;
  }
  
  .drag-handle:active {
    cursor: grabbing;
  }
  
  .drop-zone {
    opacity: 0;
    pointer-events: auto;
  }
  
  .drop-zone:hover,
  .drop-zone.drag-over {
    opacity: 1;
  }
  
  .draggable-item-container {
    position: relative;
  }
  
  .children-container {
    margin-left: 0px;
    position: relative;
  }
  
  .draggable-item-container {
    position: relative;
  }
  
  .level-0 {
    border-left: none;
  }
  
  .level-1 .outline-item {
    border-left: 2px solid #e5e7eb;
    margin-left: 8px;
  }
  
  .level-2 .outline-item {
    border-left: 2px solid #d1d5db;
    margin-left: 8px;
  }
  
  .level-3 .outline-item {
    border-left: 2px solid #c1c5c9;
    margin-left: 8px;
  }
  
  /* 展开/收起按钮样式 */
  .expand-button {
    transition: transform 0.2s ease;
  }
  
  .expand-button:hover {
    transform: scale(1.1);
  }
  
  /* 拖拽时隐藏展开按钮的交互 */
  .dragging .expand-button {
    pointer-events: none;
  }
`

// 注入样式到页面
export const injectDragStyles = () => {
  if (typeof document !== 'undefined') {
    const styleElement = document.createElement('style')
    styleElement.textContent = dragStyles
    if (!document.head.querySelector('style[data-drag-styles]')) {
      styleElement.setAttribute('data-drag-styles', 'true')
      document.head.appendChild(styleElement)
    }
  }
}
