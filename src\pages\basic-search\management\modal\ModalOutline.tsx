import { Modal } from '@/components/business/modal'
import { But<PERSON> } from '@/components/ui/button'
import { Text, TextEnum } from '@/components/business/text'
import {
  ChevronDownIcon,
  EyeSlashIcon,
  EyeIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline'
import { XMarkIcon, CheckIcon, Bars3Icon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { useEffect, useState } from 'react'
import { OutlineItem, useDeepResearchStore } from '@/store/deep-research'
import { Loader } from 'lucide-react'
// import { confirmRequirementApi } from '@/api/deep-research'
import clsx from 'clsx'
import { Textinput } from '@/components/business/text-input'

// 添加拖拽样式
const dragStyles = `
  .dragging {
    opacity: 0.5 !important;
    transform: rotate(2deg) !important;
    z-index: 1000 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }

  .drag-over {
    border-left-color: #3b82f6 !important;
    background-color: rgba(59, 130, 246, 0.1) !important;
  }

  .drag-handle {
    cursor: grab;
  }

  .drag-handle:active {
    cursor: grabbing;
  }

  .drop-zone {
    opacity: 0;
    pointer-events: auto;
  }

  .drop-zone:hover,
  .drop-zone.drag-over {
    opacity: 1;
  }

  .draggable-item-container {
    position: relative;
  }

  .children-container {
    margin-left: 0px;
    position: relative;
  }

  .draggable-item-container {
    position: relative;
  }

  .level-0 {
    border-left: none;
  }

  .level-1 .outline-item {
    border-left: 2px solid #e5e7eb;
    margin-left: 8px;
  }

  .level-2 .outline-item {
    border-left: 2px solid #d1d5db;
    margin-left: 8px;
  }

  .level-3 .outline-item {
    border-left: 2px solid #c1c5c9;
    margin-left: 8px;
  }

  /* 展开/收起按钮样式 */
  .expand-button {
    transition: transform 0.2s ease;
  }

  .expand-button:hover {
    transform: scale(1.1);
  }

  /* 拖拽时隐藏展开按钮的交互 */
  .dragging .expand-button {
    pointer-events: none;
  }
`

// 注入样式
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = dragStyles
  if (!document.head.querySelector('style[data-drag-styles]')) {
    styleElement.setAttribute('data-drag-styles', 'true')
    document.head.appendChild(styleElement)
  }
}

export const ModalOutline = ({
  open,
  handleCancel,
}: {
  open: boolean
  handleCancel: () => void
}) => {
  const { t } = useTranslation(Namespace.DEEPRESEARCH)
  const { requirement, outline, setOutline } = useDeepResearchStore()
  const [isFetched, setIsFetched] = useState(false)
  const [inputValue, setInputValue] = useState(requirement.问题确认)
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const [showDescriptions, setShowDescriptions] = useState(true)
  const [dragState, setDragState] = useState({
    draggedItem: null as OutlineItem | null,
    draggedPath: null as number[] | null, // 拖拽项目的路径 [parentIndex, childIndex]
    dropTargetPath: null as number[] | null, // 放置目标的路径
    dropPosition: null as 'before' | 'after' | 'inside' | null, // 放置位置
    isDragging: false,
  })

  // 更新大纲项目内容的函数
  const updateOutlineItem = (itemId: string, field: 'title' | 'description', value: string) => {
    if (!outline.报告大纲) return

    const updateItemRecursive = (items: OutlineItem[]): OutlineItem[] => {
      return items.map((item) => {
        if (item.id === itemId) {
          return {
            ...item,
            [field]: value,
          }
        }
        if (item.children) {
          return {
            ...item,
            children: updateItemRecursive(item.children),
          }
        }
        return item
      })
    }

    const updatedItems = updateItemRecursive(outline.报告大纲)
    const newOutline = {
      ...outline,
      报告大纲: updatedItems,
    }
    setOutline(newOutline)
  }
  // 递归收集所有项目的sequence用于默认展开
  const collectAllSequences = (items: OutlineItem[]): string[] => {
    const sequences: string[] = []
    const traverse = (item: OutlineItem) => {
      if (item.sequence) {
        sequences.push(item.sequence)
      }
      if (item.children) {
        item.children.forEach(traverse)
      }
    }
    items.forEach(traverse)
    return sequences
  }

  useEffect(() => {
    if (open) {
      setInputValue(requirement.问题确认)
      // 默认展开所有项目
      if (outline.报告大纲) {
        const allSequences = collectAllSequences(outline.报告大纲)
        setExpandedItems(new Set(allSequences))
      }
    } else {
      setIsFetched(false)
    }
  }, [open, outline.报告大纲])
  const onConfirm = () => {
    // setIsFetched(true)
    // const timer = setInterval(() => {
    //   setTime((prev) => {
    //     if (prev <= 1) {
    //       clearInterval(timer)
    //       return 0
    //     }
    //     return prev - 1
    //   })
    // }, 1000)
    // // 直接执行
    // confirmRequirementApi({
    //   confirmation: inputValue,
    //   taskId,
    //   user_language: requirement.用户语言,
    //   question: requirement.原始问题,
    // })
  }
  const toggleExpand = (sequence: string) => {
    setExpandedItems((prev) => {
      const newExpandedItems = new Set(prev)
      if (newExpandedItems.has(sequence)) {
        newExpandedItems.delete(sequence) // 收起
      } else {
        newExpandedItems.add(sequence) // 展开
      }
      return newExpandedItems
    })
  }

  // 拖拽事件处理函数
  const handleDragStart = (e: React.DragEvent, item: OutlineItem, path: number[]) => {
    e.dataTransfer.setData(
      'text/plain',
      JSON.stringify({
        itemId: item.id,
        sourcePath: path,
      }),
    )
    e.dataTransfer.effectAllowed = 'move'

    setDragState({
      draggedItem: item,
      draggedPath: path,
      dropTargetPath: null,
      dropPosition: null,
      isDragging: true,
    })

    // 添加拖拽样式
    ;(e.currentTarget as HTMLElement).classList.add('dragging')
  }

  const handleDragOver = (
    e: React.DragEvent,
    targetPath: number[],
    position: 'before' | 'after' | 'inside',
  ) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'

    // 防止拖拽到自身位置或自身的子项
    if (!isValidDropTarget(dragState.draggedPath, targetPath, position)) {
      return
    }

    setDragState((prev) => ({
      ...prev,
      dropTargetPath: targetPath,
      dropPosition: position,
    }))

    // 添加视觉反馈
    const target = e.currentTarget as HTMLElement
    target.classList.add('drag-over')
  }

  const handleDragLeave = (e: React.DragEvent) => {
    const target = e.currentTarget as HTMLElement
    target.classList.remove('drag-over')
  }

  const handleDrop = (
    e: React.DragEvent,
    targetPath: number[],
    position: 'before' | 'after' | 'inside',
  ) => {
    e.preventDefault()

    try {
      const dragData = JSON.parse(e.dataTransfer.getData('text/plain'))
      const sourcePath = dragData.sourcePath

      if (isValidDropTarget(sourcePath, targetPath, position) && outline.报告大纲) {
        reorderNestedOutlineItems(sourcePath, targetPath, position)
      }
    } catch (error) {
      console.error('拖拽数据解析失败:', error)
    }

    cleanupDragState()
  }

  const handleDragEnd = (e: React.DragEvent) => {
    cleanupDragState()
    ;(e.currentTarget as HTMLElement).classList.remove('dragging')
  }

  const cleanupDragState = () => {
    setDragState({
      draggedItem: null,
      draggedPath: null,
      dropTargetPath: null,
      dropPosition: null,
      isDragging: false,
    })

    // 清除所有拖拽相关样式
    document.querySelectorAll('.dragging, .drag-over').forEach((el) => {
      el.classList.remove('dragging', 'drag-over')
    })
  }

  // 验证拖拽目标是否有效
  const isValidDropTarget = (
    sourcePath: number[] | null,
    targetPath: number[],
    position: 'before' | 'after' | 'inside',
  ): boolean => {
    console.log('position', position)
    if (!sourcePath) return false

    // 不能拖拽到自身
    if (JSON.stringify(sourcePath) === JSON.stringify(targetPath)) {
      return false
    }

    // 不能拖拽到自身的子项中
    if (targetPath.length > sourcePath.length) {
      const isChild = sourcePath.every((val, index) => val === targetPath[index])
      if (isChild) return false
    }

    return true
  }

  // 根据路径获取项目
  const getItemByPath = (items: OutlineItem[], path: number[]): OutlineItem | null => {
    let current = items
    let item: OutlineItem | null = null

    for (let i = 0; i < path.length; i++) {
      if (!current[path[i]]) return null
      item = current[path[i]]
      if (i < path.length - 1) {
        current = item.children || []
      }
    }

    return item
  }

  // 根据路径移除项目
  const removeItemByPath = (items: OutlineItem[], path: number[]): OutlineItem | null => {
    if (path.length === 1) {
      return items.splice(path[0], 1)[0]
    }

    const parentPath = path.slice(0, -1)
    const parent = getItemByPath(items, parentPath)
    if (parent && parent.children) {
      return parent.children.splice(path[path.length - 1], 1)[0]
    }

    return null
  }

  // 在指定路径插入项目
  const insertItemAtPath = (
    items: OutlineItem[],
    item: OutlineItem,
    targetPath: number[],
    position: 'before' | 'after' | 'inside',
  ) => {
    if (position === 'inside') {
      // 插入为子项
      const target = getItemByPath(items, targetPath)
      if (target) {
        if (!target.children) target.children = []
        target.children.push(item)
      }
    } else {
      // 插入为同级项目
      if (targetPath.length === 1) {
        // 顶级项目
        const insertIndex = position === 'before' ? targetPath[0] : targetPath[0] + 1
        items.splice(insertIndex, 0, item)
      } else {
        // 子级项目
        const parentPath = targetPath.slice(0, -1)
        const parent = getItemByPath(items, parentPath)
        if (parent && parent.children) {
          const insertIndex =
            position === 'before'
              ? targetPath[targetPath.length - 1]
              : targetPath[targetPath.length - 1] + 1
          parent.children.splice(insertIndex, 0, item)
        }
      }
    }
  }

  // 嵌套重排序逻辑
  const reorderNestedOutlineItems = (
    sourcePath: number[],
    targetPath: number[],
    position: 'before' | 'after' | 'inside',
  ) => {
    if (!outline.报告大纲) return

    const items = JSON.parse(JSON.stringify(outline.报告大纲)) // 深拷贝

    // 移除源项目
    const draggedItem = removeItemByPath(items, sourcePath)
    if (!draggedItem) return

    // 调整目标路径（如果源项目在目标项目之前，需要调整索引）
    const adjustedTargetPath = [...targetPath]
    if (sourcePath.length === targetPath.length) {
      // 同级移动
      const lastSourceIndex = sourcePath[sourcePath.length - 1]
      const lastTargetIndex = targetPath[targetPath.length - 1]

      if (lastSourceIndex < lastTargetIndex) {
        adjustedTargetPath[adjustedTargetPath.length - 1] = lastTargetIndex - 1
      }
    }

    // 插入到目标位置
    insertItemAtPath(items, draggedItem, adjustedTargetPath, position)

    // 重新处理数据（重新计算序号等）
    const processedItems = processOutlineDataRecursive(items)

    // 更新 store
    const newOutline = {
      ...outline,
      报告大纲: processedItems,
    }
    setOutline(newOutline)
    console.log('嵌套重排序后的数据:', processedItems)
  }

  // 递归处理大纲数据，重新计算序号
  const processOutlineDataRecursive = (
    items: OutlineItem[],
    level = 0,
    parentSequence = '',
  ): OutlineItem[] => {
    return items.map((item, index) => {
      const currentSequence = parentSequence ? `${parentSequence}.${index + 1}` : `${index + 1}`
      return {
        ...item,
        level,
        sequence: currentSequence,
        children: item.children
          ? processOutlineDataRecursive(item.children, level + 1, currentSequence)
          : undefined,
      }
    })
  }

  // 渲染可拖拽的大纲项目（递归）
  const renderDraggableOutlineItem = (
    item: OutlineItem,
    path: number[],
    level: number,
  ): React.ReactNode => {
    const isBeingDragged =
      dragState.draggedPath && JSON.stringify(dragState.draggedPath) === JSON.stringify(path)
    const isDropTarget =
      dragState.dropTargetPath && JSON.stringify(dragState.dropTargetPath) === JSON.stringify(path)
    const isExpanded = expandedItems.has(item.sequence as string)
    const hasChildren = item.children && item.children.length > 0

    return (
      <div key={item.id} className={`draggable-item-container level-${level}`}>
        {/* 拖拽区域 - 上方 */}
        <div
          className={clsx(
            'drop-zone drop-zone-before h-2 transition-all duration-200',
            isDropTarget &&
              dragState.dropPosition === 'before' &&
              'border-t-2 border-blue-500 bg-blue-200',
          )}
          onDragOver={(e) => handleDragOver(e, path, 'before')}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, path, 'before')}
        />

        {/* 主要内容区域 */}
        <div
          draggable={true}
          className={clsx(
            'outline-item group relative cursor-move border-l-2 border-transparent transition-all duration-200 hover:border-blue-300',
            isBeingDragged && 'z-50 rotate-1 opacity-50',
            isDropTarget && dragState.dropPosition === 'inside' && 'border-blue-500 bg-blue-50',
          )}
          style={{ marginLeft: `${level * 20}px` }}
          onDragStart={(e) => handleDragStart(e, item, path)}
          onDragOver={(e) => handleDragOver(e, path, 'inside')}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, path, 'inside')}
          onDragEnd={handleDragEnd}>
          {/* 拖拽手柄 */}
          <div className='drag-handle absolute left-1 top-1/2 -translate-y-1/2 transform opacity-0 transition-opacity duration-200 group-hover:opacity-100'>
            <Bars3Icon className='h-3 w-3 text-gray-400 hover:text-blue-500' />
          </div>

          {/* 内容 */}
          <div className='ml-6 flex items-center py-1'>
            {/* 展开/收起按钮 */}
            {hasChildren ? (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  e.preventDefault()
                  toggleExpand(item.sequence as string)
                }}
                className='expand-button mr-2 flex h-4 w-4 items-center justify-center rounded hover:bg-gray-100'
                draggable={false}>
                {isExpanded ? (
                  <ChevronDownIcon className='h-3 w-3 text-gray-500' />
                ) : (
                  <ChevronRightIcon className='h-3 w-3 text-gray-500' />
                )}
              </button>
            ) : (
              <span className='mr-2 h-4 w-4'></span>
            )}

            {/* 标题 */}
            <div className='line-clamp-1 flex-1 text-sm font-medium text-secondary-black-1'>
              {item.sequence}. {item.title}
            </div>
          </div>
        </div>

        {/* 子项目 - 带展开/收起动画 */}
        {hasChildren && (
          <div
            className={clsx(
              'children-container overflow-hidden transition-all duration-300 ease-in-out',
              isExpanded ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0',
            )}
            style={{ perspective: '1000px' }}>
            <div
              className='transition-all duration-300 ease-in-out'
              style={{
                transform: isExpanded ? 'rotateX(0deg) scaleY(1)' : 'rotateX(-90deg) scaleY(0.8)',
                transformOrigin: 'center top',
                opacity: isExpanded ? 1 : 0,
              }}>
              {item.children!.map((child, childIndex) =>
                renderDraggableOutlineItem(child, [...path, childIndex], level + 1),
              )}
            </div>
          </div>
        )}

        {/* 拖拽区域 - 下方 */}
        <div
          className={clsx(
            'drop-zone drop-zone-after h-2 transition-all duration-200',
            isDropTarget &&
              dragState.dropPosition === 'after' &&
              'border-b-2 border-primary bg-primary-hover',
          )}
          onDragOver={(e) => handleDragOver(e, path, 'after')}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, path, 'after')}
        />
      </div>
    )
  }

  const renderOutline = ({
    item,
    isShowDescription,
    isLast = false,
    level = 0,
  }: {
    item: OutlineItem
    isShowDescription: boolean
    isLast?: boolean
    level?: number
  }) => {
    const isExpanded = expandedItems.has(item.sequence as string)

    return (
      <div key={item.id} className={clsx('relative', level === 0 && !isLast && 'pb-2')}>
        {/* 时间线样式 - 仅在显示描述且为子级时显示 */}
        {isShowDescription && level > 0 && (
          <div className='absolute left-3 top-1.5 flex h-full flex-col items-center'>
            {/* 圆点 */}
            <div className='relative z-10 mt-0 h-2 w-2 rounded-full bg-primary-disabled'></div>
            {/* 连接线 - 如果不是最后一个项目则显示 */}
            {!isLast && <div className='h-full w-0.5 bg-primary-hover'></div>}
          </div>
        )}

        <div className={clsx('relative', isShowDescription && level > 0 && 'ml-8')}>
          <div
            className={clsx(
              'flex items-start leading-7',
              isShowDescription ? 'items-center justify-between' : '',
            )}>
            {!isShowDescription &&
              item?.children &&
              item.children.length > 0 &&
              (isExpanded ? (
                <ChevronDownIcon
                  className='mr-1 size-4 translate-y-1.5 cursor-pointer'
                  onClick={() => {
                    toggleExpand(item.sequence as string)
                  }}
                />
              ) : (
                <ChevronRightIcon
                  className='mr-1 size-4 translate-y-1.5 cursor-pointer'
                  onClick={() => {
                    toggleExpand(item.sequence as string)
                  }}
                />
              ))}
            {isShowDescription ? (
              <div className='text-sm text-secondary-black-3'>Chapter {item.sequence}</div>
            ) : (
              <div>
                <span className='mr-1'>{item.sequence}</span>
                {item.title}
              </div>
            )}

            {isShowDescription && (
              <div className='ml-5 flex items-center text-secondary-black-3'>
                {item?.children && item.children.length > 0 && (
                  <div
                    className='flex cursor-pointer items-center'
                    onClick={() => {
                      toggleExpand(item.sequence as string)
                    }}>
                    <ChevronRightIcon
                      className={clsx(
                        'mr-1 size-4 cursor-pointer transition-transform duration-300 ease-in-out',
                        isExpanded ? 'rotate-90' : 'rotate-0',
                      )}
                    />
                    {isExpanded ? '收起' : '展开'}
                  </div>
                )}
              </div>
            )}
          </div>
          {isShowDescription && (
            <div className='mb-2 mt-1 rounded-md border border-border pb-2 text-secondary-black-2'>
              <div className='font-bold text-secondary-black-1'>
                <Textinput
                  placeholder={t('modal.titlePlaceholder') || '请输入标题'}
                  isStopEnterPrevent={false}
                  className='border-none px-2 text-[16px] leading-6 text-secondary-black-2 ring-offset-transparent focus-visible:ring-0 focus-visible:ring-offset-0'
                  value={item.title}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                    updateOutlineItem(item.id!, 'title', e.target.value)
                  }
                />
              </div>
              <Textinput
                placeholder={t('modal.descriptionPlaceholder')}
                isStopEnterPrevent={false}
                className='border-none px-2 py-0 text-sm leading-6 text-secondary-black-2 ring-offset-transparent focus-visible:ring-0 focus-visible:ring-offset-0'
                value={item.description}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                  updateOutlineItem(item.id!, 'description', e.target.value)
                }
              />
            </div>
          )}
          {item.children && (
            <div
              className={clsx(
                'overflow-hidden transition-all duration-300 ease-in-out',
                isShowDescription ? 'ml-0' : 'ml-9',
                isExpanded ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0',
              )}
              style={{ perspective: '1000px' }}>
              <div
                className='transition-all duration-300 ease-in-out'
                style={{
                  transform: isExpanded ? 'rotateX(0deg) scaleY(1)' : 'rotateX(-90deg) scaleY(0.8)',
                  transformOrigin: 'center top',
                  opacity: isExpanded ? 1 : 0,
                }}>
                {item.children.map((child, index) =>
                  renderOutline({
                    item: child,
                    isShowDescription,
                    isLast: index === item.children!.length - 1,
                    level: level + 1,
                  }),
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }
  return (
    <Modal open={open} className='w-[1200px] break-all'>
      <div>
        <Text
          type={TextEnum.H4}
          className='flex items-center justify-between border-b border-border px-6 py-4'>
          <div>
            <p>Confirm Report Outline</p>
            <p className='text-[12px] font-normal leading-5 text-secondary-black-3'>
              Enter your research query below to start your AI-powered deep research
            </p>
          </div>
          <div className='flex items-center gap-4'>
            <a
              onClick={() => {
                if (expandedItems.size === 0) {
                  // 当前是收起状态，执行展开全部
                  if (outline.报告大纲) {
                    const allSequences = collectAllSequences(outline.报告大纲)
                    setExpandedItems(new Set(allSequences))
                  }
                } else {
                  // 当前是展开状态，执行收起全部
                  setExpandedItems(new Set())
                }
              }}
              className='flex cursor-pointer select-none items-center text-sm font-normal'>
              <ChevronRightIcon
                className={clsx(
                  'mr-1 size-4 transition-transform duration-300 ease-in-out',
                  expandedItems.size === 0 ? 'rotate-0' : 'rotate-90',
                )}
              />
              {expandedItems.size === 0 ? 'Expand All' : 'Collapse All'}
            </a>
            <a
              onClick={() => {
                setShowDescriptions(!showDescriptions)
              }}
              className='flex cursor-pointer select-none items-center text-sm font-normal'>
              {showDescriptions ? (
                <>
                  <EyeSlashIcon className='mr-1 size-4'></EyeSlashIcon>
                  Hide All Descriptions
                </>
              ) : (
                <>
                  <EyeIcon className='mr-1 size-4'></EyeIcon>
                  Show All Descriptions
                </>
              )}
            </a>
            <XMarkIcon onClick={handleCancel} className='h-5 w-5 cursor-pointer' />
          </div>
        </Text>
        <div className='pb-4'>
          <div className='flex h-[calc(100vh-200px)] px-2'>
            <div
              className='h-full w-[calc(100%-300px)] overflow-auto border-r border-border p-4'
              style={{ scrollbarGutter: 'stable' }}>
              {outline.报告大纲?.map((item, index) =>
                renderOutline({
                  item,
                  isShowDescription: showDescriptions,
                  isLast: index === outline.报告大纲!.length - 1,
                  level: 0,
                }),
              )}
            </div>
            <div
              className='flex h-full w-[300px] flex-col gap-2 overflow-auto p-4 pr-2 text-secondary-black-3'
              style={{ scrollbarGutter: 'stable' }}>
              <Text type={TextEnum.H5} className='text-secondary-black-1'>
                Table of Contents Preview
              </Text>
              <div className='draggable-outline-container'>
                {outline.报告大纲?.map((item, index) =>
                  renderDraggableOutlineItem(item, [index], 0),
                )}
              </div>
            </div>
          </div>
          <div className='border-t border-border px-6 pt-4 text-right'>
            <Button variant='secondary' size='sm' className='px-4' onClick={handleCancel}>
              {t('modal.cancel')}
            </Button>
            <Button
              size='sm'
              className='ml-2 px-4'
              disabled={!inputValue || !requirement.合规判定 || isFetched}
              onClick={() => {
                onConfirm()
              }}>
              {isFetched ? (
                <Loader className='mr-1 h-4 w-4 animate-spin360' />
              ) : (
                <CheckIcon className='mr-1 size-4'></CheckIcon>
              )}
              {t('modal.confirm')}
              {/* {isFetched && time + 's'} */}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  )
}
